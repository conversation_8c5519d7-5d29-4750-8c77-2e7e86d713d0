https://github.com/0xDAEF0F/job-searchoor/blob/master/README.md
https://github.com/1Panel-dev/mcp-1panel/blob/master/README.md
https://github.com/8beeeaaat/touchdesigner-mcp/blob/master/README.md
https://github.com/8enSmith/mcp-open-library/blob/master/README.md
https://github.com/13rac1/videocapture-mcp/blob/master/README.md
https://github.com/21st-dev/magic-mcp/blob/master/README.md
https://github.com/66julienmartin/MCP-server-Deepseek_R1/blob/master/README.md
https://github.com/66julienmartin/MCP-server-Qwen_Max/blob/master/README.md
https://github.com/aantti/mcp-netbird/blob/master/README.md
https://github.com/aarora79/aws-cost-explorer-mcp-server/blob/master/README.md
https://github.com/abap-ai/mcp/blob/master/README.md
https://github.com/abel9851/mcp-server-mariadb/blob/master/README.md
https://github.com/abhiz123/todoist-mcp-server/blob/master/README.md
https://github.com/achandmsft/blob/master/README.md
https://github.com/aci-labs/ms-fabric-mcp/blob/master/README.md
https://github.com/acryldata/mcp-server-datahub/blob/master/README.md
https://github.com/adapoet/fabric-mcp-server/blob/master/README.md
https://github.com/adenot/mcp-google-search/blob/master/README.md
https://github.com/adepanges/teamretro-mcp-server/blob/master/README.md
https://github.com/adityak74/mcp-scholarly/blob/master/README.md
https://github.com/aekanun2020/mcp-server//blob/master/README.md
https://github.com/agentrpc/agentrpc/blob/master/README.md
https://github.com/agentset-ai/mcp-server/blob/master/README.md
https://github.com/agree-able/room-mcp/blob/master/README.md
https://github.com/ahnlabio/bicscan-mcp/blob/master/README.md
https://github.com/ahujasid/ableton-mcp/blob/master/README.md
https://github.com/ahujasid/blender-mcp/blob/master/README.md
https://github.com/alexarevalo9/ticktick-mcp-server/blob/master/README.md
https://github.com/algolia/mcp/blob/master/README.md
https://github.com/alibaba/higress/tree/main/plugins/wasm-go/mcp-servers/blob/master/README.md
https://github.com/aliyun/alibaba-cloud-ops-mcp-server/blob/master/README.md
https://github.com/aliyun/alibabacloud-adb-mysql-mcp-server/blob/master/README.md
https://github.com/aliyun/alibabacloud-adbpg-mcp-server/blob/master/README.md
https://github.com/aliyun/alibabacloud-dataworks-mcp-server/blob/master/README.md
https://github.com/aliyun/alibabacloud-hologres-mcp-server/blob/master/README.md
https://github.com/aliyun/alibabacloud-opensearch-mcp-server/blob/master/README.md
https://github.com/aliyun/alibabacloud-rds-openapi-mcp-server/blob/master/README.md
https://github.com/allen-munsch/mcp-prefect/blob/master/README.md
https://github.com/alpacahq/alpaca-mcp-server/blob/master/README.md
https://github.com/amornpan/py-mcp-line/blob/master/README.md
https://github.com/amornpan/py-mcp-mssql/blob/master/README.md
https://github.com/amurshak/podbeanMCP/blob/master/README.md
https://github.com/anaisbetts/mcp-installer/blob/master/README.md
https://github.com/andrea9293/mcp-documentation-server/blob/master/README.md
https://github.com/anirbanbasu/frankfurtermcp/blob/master/README.md
https://github.com/anoncam/linear-mcp/blob/master/README.md
https://github.com/anshumax/world_bank_mcp_server/blob/master/README.md
https://github.com/antvis/mcp-server-chart/blob/master/README.md
https://github.com/apache/doris-mcp-server/blob/master/README.md
https://github.com/apache/iotdb-mcp-server/blob/master/README.md
https://github.com/apappascs/blob/master/README.md
https://github.com/apappascs/mcp-servers-hub/blob/master/README.md
https://github.com/apeyroux/mcp-xmind/blob/master/README.md
https://github.com/apify/actors-mcp-server/blob/master/README.md
https://github.com/apify/mcp-server-rag-web-browser/blob/master/README.md
https://github.com/apimatic/apimatic-validator-mcp/blob/master/README.md
https://github.com/apollographql/apollo-mcp-server//blob/master/README.md
https://github.com/appcypher/blob/master/README.md
https://github.com/appcypher/awesome-mcp-servers/blob/master/README.md
https://github.com/aqara/aqara-mcp-server//blob/master/README.md
https://github.com/aquarius-wing/actor-critic-thinking-mcp/blob/master/README.md
https://github.com/ariadng/metatrader-mcp-server/blob/master/README.md
https://github.com/armorwallet/armor-crypto-mcp/blob/master/README.md
https://github.com/arturborycki/mcp-teradata/blob/master/README.md
https://github.com/asgardeo/asgardeo-mcp-server/blob/master/README.md
https://github.com/ashiknesin/pushover-mcp/blob/master/README.md
https://github.com/asusevski/opendota-mcp-server/blob/master/README.md
https://github.com/atharvagupta2003/mcp-stripe/blob/master/README.md
https://github.com/atla-ai/atla-mcp-server/blob/master/README.md
https://github.com/atlanhq/agent-toolkit/tree/main/modelcontextprotocol/blob/master/README.md
https://github.com/auth0/auth0-mcp-server/blob/master/README.md
https://github.com/aws-samples/sample-mcp-server-s3/blob/master/README.md
https://github.com/awslabs/mcp/blob/master/README.md
https://github.com/axiomhq/mcp-server-axiom/blob/master/README.md
https://github.com/AbhiJ2706/f1-mcp/tree/main/blob/master/README.md
https://github.com/AdbC99/ai-bible/blob/master/README.md
https://github.com/Adfin-Engineering/mcp-server-adfin/blob/master/README.md
https://github.com/Adity-star/mcp-yfinance-server/blob/master/README.md
https://github.com/AiondaDotCom/mcp-ssh/blob/master/README.md
https://github.com/Aiven-Open/mcp-aiven/blob/master/README.md
https://github.com/AI-Agent-Hub/ai-agent-marketplace-index-mcp/blob/master/README.md
https://github.com/AI-Agent-Hub/mcp-marketplace/blob/master/README.md
https://github.com/AI-QL/blob/master/README.md
https://github.com/AI-QL/chat-mcp/blob/master/README.md
https://github.com/Alation/alation-ai-agent-sdk/blob/master/README.md
https://github.com/Arize-ai/phoenix/tree/main/js/packages/phoenix-mcp/blob/master/README.md
https://github.com/AshDevFr/discourse-mcp-server/blob/master/README.md
https://github.com/AshwinSundar/congress_gov_mcp/blob/master/README.md
https://github.com/AudienseCo/mcp-audiense-insights/blob/master/README.md
https://github.com/Automata-Labs-team/code-sandbox-mcp/blob/master/README.md
https://github.com/Azure-Samples/mcp/blob/master/README.md
https://github.com/Azure/azure-mcp/blob/master/README.md
https://github.com/badkk/blob/master/README.md
https://github.com/badkk/awesome-crypto-mcp-servers/blob/master/README.md
https://github.com/baidu-maps/mcp/blob/master/README.md
https://github.com/baidubce/app-builder/tree/master/python/mcp_server/ai_search/blob/master/README.md
https://github.com/bankless/onchain-mcp/blob/master/README.md
https://github.com/baryhuang/mcp-headless-gmail/blob/master/README.md
https://github.com/baryhuang/mcp-remote-macos-use/blob/master/README.md
https://github.com/baryhuang/mcp-server-any-openapi/blob/master/README.md
https://github.com/baryhuang/mcp-server-aws-resources-python/blob/master/README.md
https://github.com/basicmachines-co/basic-memory/blob/master/README.md
https://github.com/bazinga012/mcp_code_executor/blob/master/README.md
https://github.com/benborla/mcp-server-mysql/blob/master/README.md
https://github.com/bharathvaj-ganesan/whois-mcp/blob/master/README.md
https://github.com/bigcodegen/mcp-neovim-server/blob/master/README.md
https://github.com/bitrise-io/bitrise-mcp/blob/master/README.md
https://github.com/bnb-chain/bnbchain-mcp/blob/master/README.md
https://github.com/boldsign/boldsign-mcp/blob/master/README.md
https://github.com/boostspace/boostspace-mcp-server/blob/master/README.md
https://github.com/box-community/mcp-server-box/blob/master/README.md
https://github.com/browserbase/mcp-server-browserbase/blob/master/README.md
https://github.com/browserstack/mcp-server/blob/master/README.md
https://github.com/bucketco/bucket-javascript-sdk/tree/main/packages/cli#model-context-protocol/blob/master/README.md
https://github.com/builtwith/mcp/blob/master/README.md
https://github.com/burningion/video-editing-mcp/blob/master/README.md
https://github.com/buryhuang/mcp-hubspot/blob/master/README.md
https://github.com/bytebase/dbhub//blob/master/README.md
https://github.com/BfdCampos/monzo-mcp-bfdcampos/blob/master/README.md
https://github.com/Boston343/starwind-ui-mcp//blob/master/README.md
https://github.com/calclavia/blob/master/README.md
https://github.com/calclavia/mcp-obsidian/blob/master/README.md
https://github.com/calvernaz/alphavantage/blob/master/README.md
https://github.com/campertunity/mcp-server/blob/master/README.md
https://github.com/carterlasalle/mac_messages_mcp/blob/master/README.md
https://github.com/cartesia-ai/cartesia-mcp/blob/master/README.md
https://github.com/cashfree/cashfree-mcp/blob/master/README.md
https://github.com/cbinsights/cbi-mcp-server/blob/master/README.md
https://github.com/chaindead/telegram-mcp/blob/master/README.md
https://github.com/chaitin/SafeLine/tree/main/mcp_server/blob/master/README.md
https://github.com/chargebee/agentkit/tree/main/modelcontextprotocol/blob/master/README.md
https://github.com/chatmcp/mcp-directory/blob/master/README.md
https://github.com/cheqd/mcp-toolkit/blob/master/README.md
https://github.com/chigwell/telegram-mcp/blob/master/README.md
https://github.com/chroma-core/chroma-mcp/blob/master/README.md
https://github.com/ckreiling/mcp-server-docker/blob/master/README.md
https://github.com/clafollett/fdic-bank-find-mcp-server/blob/master/README.md
https://github.com/classfang/ssh-mcp-server/blob/master/README.md
https://github.com/cloudera/iceberg-mcp-server/blob/master/README.md
https://github.com/cloudflare/mcp-server-cloudflare/blob/master/README.md
https://github.com/cnych/seo-mcp/blob/master/README.md
https://github.com/co-browser/attestable-mcp-server/blob/master/README.md
https://github.com/co-browser/browser-use-mcp-server/blob/master/README.md
https://github.com/codacy/codacy-mcp-server//blob/master/README.md
https://github.com/codeboyzhou/mcp-declarative-java-sdk/blob/master/README.md
https://github.com/coder-linping/azure-wiki-search-server/blob/master/README.md
https://github.com/coding-sailor/mcp-server-hc3/blob/master/README.md
https://github.com/coingecko/coingecko-typescript/tree/main/packages/mcp-server/blob/master/README.md
https://github.com/coinpaprika/dexpaprika-mcp/blob/master/README.md
https://github.com/comet-ml/opik-mcp/blob/master/README.md
https://github.com/conductor-oss/conductor-mcp/blob/master/README.md
https://github.com/confluentinc/mcp-confluent/blob/master/README.md
https://github.com/cr7258/elasticsearch-mcp-server/blob/master/README.md
https://github.com/cswkim/discogs-mcp-server/blob/master/README.md
https://github.com/cuongtl1992/unleash-mcp/blob/master/README.md
https://github.com/cyberchitta/llm-context.py/blob/master/README.md
https://github.com/cyberchitta/scrapling-fetch-mcp/blob/master/README.md
https://github.com/cycodehq/cycode-cli#mcp-command-experiment/blob/master/README.md
https://github.com/Canner/wren-engine/blob/master/README.md
https://github.com/ChristianHinge/dicom-mcp/blob/master/README.md
https://github.com/ChristophEnglisch/keycloak-model-context-protocol/blob/master/README.md
https://github.com/ChronulusAI/chronulus-mcp/blob/master/README.md
https://github.com/ChuckBryan/ynabmcpserver/blob/master/README.md
https://github.com/CircleCI-Public/mcp-server-circleci/blob/master/README.md
https://github.com/ClickHouse/mcp-clickhouse/blob/master/README.md
https://github.com/CodeLogicIncEngineering/codelogic-mcp-server/blob/master/README.md
https://github.com/CoderGamester/mcp-unity/blob/master/README.md
https://github.com/Coding-Solo/godot-mcp/blob/master/README.md
https://github.com/ConechoAI/openai-websearch-mcp/blob/master/README.md
https://github.com/Contrast-Security-OSS/mcp-contrast/blob/master/README.md
https://github.com/Couchbase-Ecosystem/mcp-server-couchbase/blob/master/README.md
https://github.com/Cronlytic/cronlytic-mcp-server/blob/master/README.md
https://github.com/CryptoRadi/schemaflow-mcp-server/blob/master/README.md
https://github.com/CyberhavenInc/filesystem-mcpignore/blob/master/README.md
https://github.com/da-okazaki/mcp-neo4j-server/blob/master/README.md
https://github.com/da1z/docsmcp/blob/master/README.md
https://github.com/damms005/devdb-vscode?tab=readme-ov-file#mcp-configuration/blob/master/README.md
https://github.com/danield137/mcp-workflowy/blob/master/README.md
https://github.com/daobataotie/mssql-mcp/blob/master/README.md
https://github.com/data-skunks/kpu-mcp/blob/master/README.md
https://github.com/datastax/astra-db-mcp/blob/master/README.md
https://github.com/datastrato/mcp-server-gravitino/blob/master/README.md
https://github.com/daytonaio/daytona/tree/main/apps/cli/mcp/blob/master/README.md
https://github.com/ddukbg/github-enterprise-mcp/blob/master/README.md
https://github.com/debugg-ai/debugg-ai-mcp/blob/master/README.md
https://github.com/deepfates/mcp-replicate/blob/master/README.md
https://github.com/delorenj/mcp-server-ticketmaster/blob/master/README.md
https://github.com/descope-sample-apps/descope-mcp-server/blob/master/README.md
https://github.com/designcomputer/mysql_mcp_server/blob/master/README.md
https://github.com/devhub/devhub-cms-mcp/blob/master/README.md
https://github.com/devilcoder01/weather-mcp-server/blob/master/README.md
https://github.com/devonmojito/ton-blockchain-mcp/blob/master/README.md
https://github.com/devrev/mcp-server/blob/master/README.md
https://github.com/diegobit/aranet4-mcp-server/blob/master/README.md
https://github.com/dinghuazhou/sample-mcp-server-tos/blob/master/README.md
https://github.com/djannot/puppeteer-vision-mcp/blob/master/README.md
https://github.com/dmayboroda/minima/blob/master/README.md
https://github.com/dodopayments/dodopayments-node/tree/main/packages/mcp-server/blob/master/README.md
https://github.com/doggybee/mcp-server-leetcode/blob/master/README.md
https://github.com/dogukanakkaya/pulumi-mcp-server/blob/master/README.md
https://github.com/domdomegg/airtable-mcp-server/blob/master/README.md
https://github.com/dominik1001/caldav-mcp/blob/master/README.md
https://github.com/donghyun-chae/mcp-amadeus/blob/master/README.md
https://github.com/drestrepom/mcp_graphql/blob/master/README.md
https://github.com/dschuler36/reaper-mcp-server/blob/master/README.md
https://github.com/dvcrn/mcp-server-siri-shortcuts/blob/master/README.md
https://github.com/dynatrace-oss/dynatrace-mcp/blob/master/README.md
https://github.com/DappierAI/dappier-mcp/blob/master/README.md
https://github.com/DeemosTech/rodin-api-mcp/blob/master/README.md
https://github.com/DeepLcom/deepl-mcp-server/blob/master/README.md
https://github.com/DefangLabs/defang/blob/main/src/pkg/mcp/blob/master/README.md/blob/master/README.md
https://github.com/DevEnterpriseSoftware/scrapi-mcp/blob/master/README.md
https://github.com/DMontgomery40/deepseek-mcp-server/blob/master/README.md
https://github.com/Dumpling-AI/mcp-server-dumplingai/blob/master/README.md
https://github.com/e2b-dev/mcp-server/blob/master/README.md
https://github.com/edgee-cloud/mcp-server-edgee/blob/master/README.md
https://github.com/edwin-finance/edwin/tree/main/examples/mcp-server/blob/master/README.md
https://github.com/edwinbernadus/nocodb-mcp-server/blob/master/README.md
https://github.com/egyptianego17/email-mcp-server/blob/master/README.md
https://github.com/elastic/mcp-server-elasticsearch/blob/master/README.md
https://github.com/elie222/inbox-zero/tree/main/apps/mcp-server/blob/master/README.md
https://github.com/encoreshao/bamboohr-mcp/blob/master/README.md
https://github.com/eqtylab/mcp-guardian/blob/master/README.md
https://github.com/ergut/mcp-bigquery-server/blob/master/README.md
https://github.com/erhwenkuo/mcp-searxng/blob/master/README.md
https://github.com/erniebrodeur/mcp-grep/blob/master/README.md
https://github.com/ertiqah/linkedin-mcp-runner/blob/master/README.md
https://github.com/esignaturescom/mcp-server-esignatures/blob/master/README.md
https://github.com/evalstate/mcp-hfspace/blob/master/README.md
https://github.com/exa-labs/exa-mcp-server/blob/master/README.md
https://github.com/executeautomation/mcp-database-server/blob/master/README.md
https://github.com/executeautomation/mcp-playwright/blob/master/README.md
https://github.com/EduBase/MCP/blob/master/README.md
https://github.com/EnesCinr/twitter-mcp/blob/master/README.md
https://github.com/fastnai/mcp-fastn/blob/master/README.md
https://github.com/fatwang2/search1api-mcp/blob/master/README.md
https://github.com/felores/airtable-mcp/blob/master/README.md
https://github.com/felores/cloudinary-mcp-server/blob/master/README.md
https://github.com/felores/placid-mcp-server/blob/master/README.md
https://github.com/ferrislucas/iterm-mcp/blob/master/README.md
https://github.com/financial-datasets/mcp-server/blob/master/README.md
https://github.com/fingertip-com/fingertip-mcp/blob/master/README.md
https://github.com/firebase/firebase-tools/blob/master/src/mcp/blob/master/README.md
https://github.com/fireproof-storage/mcp-database-server/blob/master/README.md
https://github.com/firstorderai/authenticator_mcp/blob/master/README.md
https://github.com/fluidattacks/mcp/blob/master/README.md
https://github.com/foobara/mcp-connector/blob/master/README.md
https://github.com/foursquare/foursquare-places-mcp/blob/master/README.md
https://github.com/furey/mongodb-lens/blob/master/README.md
https://github.com/FalkorDB/FalkorDB-MCPServer/blob/master/README.md
https://github.com/FelixFoster/mcp-enhance-prompt/blob/master/README.md
https://github.com/Fewsats/fewsats-mcp/blob/master/README.md
https://github.com/Fibery-inc/fibery-mcp-server/blob/master/README.md
https://github.com/Flux159/mcp-server-kubernetes/blob/master/README.md
https://github.com/Flyworks-AI/flyworks-mcp/blob/master/README.md
https://github.com/g0t4/mcp-server-commands/blob/master/README.md
https://github.com/gaganmanku96/talk-with-figma-claude/blob/master/README.md
https://github.com/gannonh/firebase-mcp/blob/master/README.md
https://github.com/garymengcom/serper-mcp-server/blob/master/README.md
https://github.com/gching/blob/master/README.md
https://github.com/geropl/git-mcp-go/blob/master/README.md
https://github.com/geropl/linear-mcp-go/blob/master/README.md
https://github.com/ghrud92/simple-loki-mcp/blob/master/README.md
https://github.com/gitCarrot/mcp-server-aws-cognito/blob/master/README.md
https://github.com/githejie/mcp-server-calculator/blob/master/README.md
https://github.com/github/github-mcp-server/blob/master/README.md
https://github.com/gitmotion/ntfy-me-mcp/blob/master/README.md
https://github.com/glassBead-tc/audius-mcp-atris/blob/master/README.md
https://github.com/gleanwork/mcp-server/blob/master/README.md
https://github.com/gNucleus/text-to-cad-mcp/blob/master/README.md
https://github.com/goat-sdk/goat/tree/main/typescript/examples/by-framework/model-context-protocol/blob/master/README.md
https://github.com/gofireflyio/firefly-mcp/blob/master/README.md
https://github.com/gomarble-ai/facebook-ads-mcp-server/blob/master/README.md
https://github.com/gomarble-ai/google-ads-mcp-server/blob/master/README.md
https://github.com/googleapis/genai-toolbox/blob/master/README.md
https://github.com/gornskew/lisply-mcp/blob/master/README.md
https://github.com/gotohuman/gotohuman-mcp-server/blob/master/README.md
https://github.com/gpetraroli/mcp_pdf_reader/blob/master/README.md
https://github.com/grafana/mcp-grafana/blob/master/README.md
https://github.com/grafbase/grafbase/tree/main/crates/mcp/blob/master/README.md
https://github.com/graphlit/graphlit-mcp-server/blob/master/README.md
https://github.com/groundlight/mcp-vision/blob/master/README.md
https://github.com/guyru/man-mcp-server/blob/master/README.md
https://github.com/GdMacmillan/apt-mcp-server/blob/master/README.md
https://github.com/GeLi2001/datadog-mcp-server/blob/master/README.md
https://github.com/GeLi2001/shopify-mcp/blob/master/README.md
https://github.com/GeLi2001/tft-mcp-server/blob/master/README.md
https://github.com/GibsonAI/mcp/blob/master/README.md
https://github.com/GLips/Figma-Context-MCP/blob/master/README.md
https://github.com/GongRzhe/A2A-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/APIWeaver/blob/master/README.md
https://github.com/GongRzhe/Gmail-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/Human-In-the-Loop-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/Image-Generation-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/JSON-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/Langflow-DOC-QA-SERVER/blob/master/README.md
https://github.com/GongRzhe/MCP-Server-Creator/blob/master/README.md
https://github.com/GongRzhe/opencv-mcp-server/blob/master/README.md
https://github.com/GongRzhe/Office-PowerPoint-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/Office-Visio-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/Office-Word-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/Quickchart-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/REDIS-MCP-Server/blob/master/README.md
https://github.com/GongRzhe/terminal-controller-mcp/blob/master/README.md
https://github.com/GongRzhe/TRAVEL-PLANNER-MCP-Server/blob/master/README.md
https://github.com/GoogleCloudPlatform/cloud-run-mcp/blob/master/README.md
https://github.com/GoPlausible/algorand-mcp/blob/master/README.md
https://github.com/GreatScottyMac/context-portal/blob/master/README.md
https://github.com/GreptimeTeam/greptimedb-mcp-server/blob/master/README.md
https://github.com/hannesj/mcp-graphql-schema/blob/master/README.md
https://github.com/hannesj/mcp-openapi-schema/blob/master/README.md
https://github.com/hao-cyber/phone-mcp/blob/master/README.md
https://github.com/haris-musa/excel-mcp-server/blob/master/README.md
https://github.com/hashicorp/terraform-mcp-server/blob/master/README.md
https://github.com/hekmon8/blob/master/README.md
https://github.com/heroku/heroku-mcp-server/blob/master/README.md
https://github.com/heurist-network/heurist-mesh-mcp-server/blob/master/README.md
https://github.com/hichana/goalstory-mcp/blob/master/README.md
https://github.com/hiromitsusasaki/raindrop-io-mcp-server/blob/master/README.md
https://github.com/honeycombio/honeycomb-mcp/blob/master/README.md
https://github.com/horizondatawave/hdw-mcp-server/blob/master/README.md
https://github.com/horw/esp-mcp/blob/master/README.md
https://github.com/hpalma/springinitializr-mcp/blob/master/README.md
https://github.com/hungryrobot1/MCP-PIF/blob/master/README.md
https://github.com/hunter-io/hunter-mcp/blob/master/README.md
https://github.com/hyperbrowserai/mcp/blob/master/README.md
https://github.com/HarperDB/mcp-server/blob/master/README.md
https://github.com/Hexix23/shodan-mcp/blob/master/README.md
https://github.com/HumanSignal/label-studio-mcp-server/blob/master/README.md
https://github.com/HyperbolicLabs/hyperbolic-mcp/blob/master/README.md
https://github.com/idachev/mcp-javadc/blob/master/README.md
https://github.com/idoru/influxdb-mcp-server/blob/master/README.md
https://github.com/idosal/git-mcp/blob/master/README.md
https://github.com/iflytek/ifly-spark-agent-mcp/blob/master/README.md
https://github.com/iflytek/ifly-workflow-mcp-server/blob/master/README.md
https://github.com/ifuryst/rednote-mcp/blob/master/README.md
https://github.com/ihor-sokoliuk/mcp-searxng/blob/master/README.md
https://github.com/incentivai/quickchat-ai-mcp/blob/master/README.md
https://github.com/inkeep/mcp-server-python/blob/master/README.md
https://github.com/integration-app/mcp-server/blob/master/README.md
https://github.com/integromat/make-mcp-server/blob/master/README.md
https://github.com/intsig-textin/textin-mcp/blob/master/README.md
https://github.com/isaacwasserman/mcp-snowflake-server/blob/master/README.md
https://github.com/isaacwasserman/mcp-vegalite-server/blob/master/README.md
https://github.com/isdaniel/mcp_weather_server/blob/master/README.md
https://github.com/its-dart/dart-mcp-server/blob/master/README.md
https://github.com/ivnvxd/mcp-server-odoo/blob/master/README.md
https://github.com/ivo-toby/contentful-mcp/blob/master/README.md
https://github.com/IBM/wxflows/tree/main/examples/mcp/javascript/blob/master/README.md
https://github.com/InditexTech/mcp-server-simulator-ios-idb/blob/master/README.md
https://github.com/InditexTech/mcp-teams-server/blob/master/README.md
https://github.com/Inflectra/mcp-server-spira/blob/master/README.md
https://github.com/jacwu/mcp-server-aoai-dalle3/blob/master/README.md
https://github.com/jamsocket/forevervm/tree/main/javascript/mcp-server/blob/master/README.md
https://github.com/jaw9c/blob/master/README.md
https://github.com/jaw9c/awesome-remote-mcp-servers/blob/master/README.md
https://github.com/jeamee/blob/master/README.md
https://github.com/jean-technologies/smartlead-mcp-server-local/blob/master/README.md
https://github.com/jeff-nasseri/helm-chart-cli-mcp/blob/master/README.md
https://github.com/jeff-nasseri/mikrotik-mcp/blob/master/README.md
https://github.com/jerhadf/linear-mcp-server/blob/master/README.md
https://github.com/jeroenvdmeer/feyod-mcp/blob/master/README.md
https://github.com/jfrog/mcp-jfrog/blob/master/README.md
https://github.com/jifrozen0110/mcp-riot/blob/master/README.md
https://github.com/jjsantos01/jupyter-notebook-mcp/blob/master/README.md
https://github.com/jjsantos01/qgis_mcp/blob/master/README.md
https://github.com/jkosik/mcp-server-splunk/blob/master/README.md
https://github.com/jobswithgpt/mcp/blob/master/README.md
https://github.com/john-zhang-dev/xero-mcp/blob/master/README.md
https://github.com/johnpapa/mcp-starwars/blob/master/README.md
https://github.com/johnpapa/peacock-mcp/blob/master/README.md
https://github.com/jokemanfire/mcp-containerd/blob/master/README.md
https://github.com/jonathan-politzki/mcp-writer-substack/blob/master/README.md
https://github.com/jsdelivr/globalping-mcp-server/blob/master/README.md
https://github.com/junmer/mcp-server-lottiefiles/blob/master/README.md
https://github.com/jyjune/mcp_vms/blob/master/README.md
https://github.com/Jeamee/MCPHub-Desktop/blob/master/README.md
https://github.com/JetBrains/mcp-jetbrains/blob/master/README.md
https://github.com/JexinSam/mssql_mcp_server/blob/master/README.md
https://github.com/JordiNeil/mcp-databricks-server/blob/master/README.md
https://github.com/kadykov/mcp-openapi-schema-explorer/blob/master/README.md
https://github.com/kagisearch/kagimcp/blob/master/README.md
https://github.com/kanad13/MCP-Server-for-Hashing/blob/master/README.md
https://github.com/kapilduraphe/mcp-watch/blob/master/README.md
https://github.com/kapilduraphe/okta-mcp-server/blob/master/README.md
https://github.com/kapilduraphe/webflow-mcp-server/blob/master/README.md
https://github.com/keboola/keboola-mcp-server/blob/master/README.md
https://github.com/kelvin6365/plane-mcp-server/blob/master/README.md
https://github.com/kenjihikmatullah/productboard-mcp/blob/master/README.md
https://github.com/kenliao94/mcp-server-rabbitmq/blob/master/README.md
https://github.com/kiliczsh/mcp-mongo-server/blob/master/README.md
https://github.com/kiwamizamurai/mcp-kibela-server/blob/master/README.md
https://github.com/kld3v/reed_jobs_mcp/blob/master/README.md
https://github.com/knocklabs/agent-toolkit#model-context-protocol-mcp/blob/master/README.md
https://github.com/ko1ynnky/github-actions-mcp-server/blob/master/README.md
https://github.com/kocierik/consul-mcp-server/blob/master/README.md
https://github.com/kocierik/mcp-nomad/blob/master/README.md
https://github.com/kone-net/mcp_server_lark/blob/master/README.md
https://github.com/korotovsky/slack-mcp-server/blob/master/README.md
https://github.com/kubesphere/ks-mcp-server/blob/master/README.md
https://github.com/kukapay/crypto-feargreed-mcp/blob/master/README.md
https://github.com/kukapay/crypto-indicators-mcp/blob/master/README.md
https://github.com/kukapay/crypto-sentiment-mcp/blob/master/README.md
https://github.com/kukapay/cryptopanic-mcp-server/blob/master/README.md
https://github.com/kukapay/dune-analytics-mcp/blob/master/README.md
https://github.com/kukapay/freqtrade-mcp/blob/master/README.md
https://github.com/kukapay/jupiter-mcp/blob/master/README.md
https://github.com/kukapay/pancakeswap-poolspy-mcp/blob/master/README.md
https://github.com/kukapay/thegraph-mcp/blob/master/README.md
https://github.com/kukapay/token-minter-mcp/blob/master/README.md
https://github.com/kukapay/token-revoke-mcp/blob/master/README.md
https://github.com/kukapay/uniswap-poolspy-mcp/blob/master/README.md
https://github.com/kukapay/uniswap-trader-mcp/blob/master/README.md
https://github.com/kukapay/whale-tracker-mcp/blob/master/README.md
https://github.com/kurdin/github-repos-manager-mcp/blob/master/README.md
https://github.com/kurrent-io/mcp-server/blob/master/README.md
https://github.com/kuzudb/kuzu-mcp-server/blob/master/README.md
https://github.com/Klavis-AI/klavis/tree/main/mcp_servers/discord/blob/master/README.md
https://github.com/Klavis-AI/klavis/tree/main/mcp_servers/markitdown/blob/master/README.md
https://github.com/Klavis-AI/klavis/tree/main/mcp_servers/pandoc/blob/master/README.md
https://github.com/Klavis-AI/klavis/tree/main/mcp_servers/report_generation/blob/master/README.md
https://github.com/Klavis-AI/klavis/tree/main/mcp_servers/resend/blob/master/README.md
https://github.com/Klavis-AI/klavis/tree/main/mcp_servers/youtube/blob/master/README.md
https://github.com/Kong/mcp-konnect/blob/master/README.md
https://github.com/KWDB/kwdb-mcp-server/blob/master/README.md
https://github.com/KyrieTangSheng/mcp-server-nationalparks/blob/master/README.md
https://github.com/lamaalrajih/kicad-mcp/blob/master/README.md
https://github.com/lambda-capture/mcp-server/blob/master/README.md
https://github.com/langfuse/mcp-server-langfuse/blob/master/README.md
https://github.com/last9/last9-mcp-server/blob/master/README.md
https://github.com/lastmile-ai/mcp-agent/blob/master/README.md
https://github.com/launchdarkly/mcp-server/blob/master/README.md
https://github.com/lciesielski/mcp-salesforce-example/blob/master/README.md
https://github.com/leehanchung/bing-search-mcp/blob/master/README.md
https://github.com/lenwood/cfbd-mcp-server/blob/master/README.md
https://github.com/leonardsellem/n8n-mcp-server/blob/master/README.md
https://github.com/lharries/whatsapp-mcp/blob/master/README.md
https://github.com/line/line-bot-mcp-server/blob/master/README.md
https://github.com/lingodotdev/lingo.dev/blob/main/mcp.md/blob/master/README.md
https://github.com/lioarce01/trello-mcp-server/blob/master/README.md
https://github.com/lishenxydlgzs/aws-athena-mcp/blob/master/README.md
https://github.com/litmusautomation/litmus-mcp-server/blob/master/README.md
https://github.com/liuyoshio/mcp-compass/blob/master/README.md
https://github.com/liveblocks/liveblocks-mcp-server/blob/master/README.md
https://github.com/lloydzhou/bitable-mcp/blob/master/README.md
https://github.com/longmans/coin_api_mcp/blob/master/README.md
https://github.com/longyi1207/glean-mcp-server/blob/master/README.md
https://github.com/loopwork-ai/iMCP/blob/master/README.md
https://github.com/lucamauri/MediaWiki-MCP-adapter/blob/master/README.md
https://github.com/luminati-io/brightdata-mcp/blob/master/README.md
https://github.com/Laksh-star/mcp-server-tmdb/blob/master/README.md
https://github.com/Lspace-io/lspace-server/blob/master/README.md
https://github.com/LucasHild/mcp-server-bigquery/blob/master/README.md
https://github.com/Lucassssss/eechat/blob/master/README.md
https://github.com/macoughl/blob/master/README.md
https://github.com/macrat/mcp-server-kintone/blob/master/README.md
https://github.com/madupay/mcp-sanctions/blob/master/README.md
https://github.com/maestro-org/maestro-mcp/blob/master/README.md
https://github.com/magnetai/mcp-free-usdc-transfer/blob/master/README.md
https://github.com/mailgun/mailgun-mcp-server/blob/master/README.md
https://github.com/makenotion/notion-mcp-server#readme/blob/master/README.md
https://github.com/mamertofabian/elevenlabs-mcp-server/blob/master/README.md
https://github.com/mamertofabian/mcp-everything-search/blob/master/README.md
https://github.com/manusa/kubernetes-mcp-server/blob/master/README.md
https://github.com/mapbox/mcp-server/blob/master/README.md
https://github.com/marctheshark3/ergo-mcp/blob/master/README.md
https://github.com/marimo-team/codemirror-mcp/blob/master/README.md
https://github.com/mark3labs/mcp-filesystem-server/blob/master/README.md
https://github.com/mastergo-design/mastergo-magic-mcp/blob/master/README.md
https://github.com/maton-ai/agent-toolkit/tree/main/modelcontextprotocol/blob/master/README.md
https://github.com/matthewdcage/cursor-mcp-installer/blob/master/README.md
https://github.com/mattiasw/browserloop/blob/master/README.md
https://github.com/mbailey/voice-mcp/blob/master/README.md
https://github.com/mberg/kokoro-tts-mcp/blob/master/README.md
https://github.com/mckinsey/vizro/tree/main/vizro-mcp/blob/master/README.md
https://github.com/mcp-router/mcp-router/blob/master/README.md
https://github.com/mcpdotdirect/evm-mcp-server/blob/master/README.md
https://github.com/mcpdotdirect/starknet-mcp-server/blob/master/README.md
https://github.com/mcpdotdirect/template-mcp-server/blob/master/README.md
https://github.com/mcpso/mcp-server-chatsum/blob/master/README.md
https://github.com/mcpso/mcp-server-javafx/blob/master/README.md
https://github.com/mcpx-dev/mcp-badges/blob/master/README.md
https://github.com/meilisearch/meilisearch-mcp/blob/master/README.md
https://github.com/mektigboy/server-hyperliquid/blob/master/README.md
https://github.com/mem0ai/mem0-mcp/blob/master/README.md
https://github.com/memgraph/ai-toolkit/tree/main/integrations/mcp-memgraph/blob/master/README.md
https://github.com/memgraph/mcp-memgraph/blob/master/README.md
https://github.com/mendableai/firecrawl-mcp-server/blob/master/README.md
https://github.com/merill/lokka/blob/master/README.md
https://github.com/metoro-io/metoro-mcp-server/blob/master/README.md
https://github.com/metricool/mcp-metricool/blob/master/README.md
https://github.com/mfukushim/map-traveler-mcp/blob/master/README.md
https://github.com/michaellatman/blob/master/README.md
https://github.com/microsoft/clarity-mcp-server/blob/master/README.md
https://github.com/microsoftdocs/mcp/blob/master/README.md
https://github.com/milisp/mcp-linker/blob/master/README.md
https://github.com/mjochum64/mcp-solr-search/blob/master/README.md
https://github.com/mobile-next/mobile-mcp/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/aws-kb-retrieval-server/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/brave-search/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/everart/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/gdrive/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/github/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/gitlab/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/google-maps/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/postgres/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/puppeteer/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/redis/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/sentry/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/slack/blob/master/README.md
https://github.com/modelcontextprotocol/servers-archived/tree/main/src/sqlite/blob/master/README.md
https://github.com/momentohq/mcp-momento/blob/master/README.md
https://github.com/mongodb-js/mongodb-mcp-server/blob/master/README.md
https://github.com/motherduckdb/mcp-server-motherduck/blob/master/README.md
https://github.com/movstox/lazy-toggl-mcp/blob/master/README.md
https://github.com/mrexodia/user-feedback-mcp/blob/master/README.md
https://github.com/mschneider82/mcp-openweather/blob/master/README.md
https://github.com/msgbyte/tianji/tree/master/apps/mcp-server/blob/master/README.md
https://github.com/mytechnotalent/MalwareBazaar_MCP/blob/master/README.md
https://github.com/MarketplaceAdPros/amazon-ads-mcp-server/blob/master/README.md
https://github.com/MatiasVara/libvirt-mcp/blob/master/README.md
https://github.com/MFYDev/ghost-mcp/blob/master/README.md
https://github.com/Michael98671/agentbay/blob/master/README.md
https://github.com/MindscapeHQ/mcp-server-raygun/blob/master/README.md
https://github.com/Morningstar/morningstar-mcp-server/blob/master/README.md
https://github.com/Mubashwer/git-mob-mcp-server/blob/master/README.md
https://github.com/nabid-pf/mongo-mongoose-mcp/blob/master/README.md
https://github.com/nabid-pf/youtube-video-summarizer-mcp/blob/master/README.md
https://github.com/nacos-group/nacos-mcp-router/blob/master/README.md
https://github.com/nanbingxyz/blob/master/README.md
https://github.com/nanovms/ops-mcp/blob/master/README.md
https://github.com/needle-ai/needle-mcp/blob/master/README.md
https://github.com/neo4j-contrib/mcp-neo4j//blob/master/README.md
https://github.com/neondatabase/mcp-server-neon/blob/master/README.md
https://github.com/nerve-hq/nerve-mcp-server/blob/master/README.md
https://github.com/netdata/netdata/blob/master/src/web/mcp/blob/master/README.md/blob/master/README.md
https://github.com/netease-im/yunxin-mcp-server/blob/master/README.md
https://github.com/neuromechanist/matlab-mcp-tools/blob/master/README.md
https://github.com/niledatabase/nile-mcp-server/blob/master/README.md
https://github.com/nkapila6/mcp-local-rag/blob/master/README.md
https://github.com/nkapila6/mcp-meme-sticky/blob/master/README.md
https://github.com/noditlabs/nodit-mcp-server/blob/master/README.md
https://github.com/nolleh/mcp-vertica/blob/master/README.md
https://github.com/norman-finance/norman-mcp-server/blob/master/README.md
https://github.com/nota/gyazo-mcp-server/blob/master/README.md
https://github.com/nrwl/nx-console/blob/master/apps/nx-mcp/blob/master/README.md
https://github.com/nspady/google-calendar-mcp/blob/master/README.md
https://github.com/NitayRabi/fitbit-mcp/blob/master/README.md
https://github.com/NonicaTeam/AI-Connector-for-Revit/blob/master/README.md
https://github.com/oatpp/oatpp-mcp/blob/master/README.md
https://github.com/oceanbase/mcp-oceanbase/blob/master/README.md
https://github.com/offorte/offorte-mcp-server#readme/blob/master/README.md
https://github.com/ognis1205/mcp-server-unitycatalog/blob/master/README.md
https://github.com/omergocmen/json2video-mcp-server/blob/master/README.md
https://github.com/onebirdrocks/ebook-mcp/blob/master/README.md
https://github.com/oOo0oOo/lean-lsp-mcp/blob/master/README.md
https://github.com/open-strategy-partners/osp_marketing_tools/blob/master/README.md
https://github.com/openbnb-org/mcp-server-airbnb/blob/master/README.md
https://github.com/openMF/mcp-mifosx/blob/master/README.md
https://github.com/opensearch-project/opensearch-mcp-server-py/blob/master/README.md
https://github.com/opentoolsteam/blob/master/README.md
https://github.com/opgginc/opgg-mcp/blob/master/README.md
https://github.com/opslevel/opslevel-mcp/blob/master/README.md
https://github.com/optuna/optuna-mcp/blob/master/README.md
https://github.com/oschina/mcp-gitee/blob/master/README.md
https://github.com/osomai/servicenow-mcp/blob/master/README.md
https://github.com/oxylabs/oxylabs-mcp/blob/master/README.md
https://github.com/OctagonAI/octagon-mcp-server/blob/master/README.md
https://github.com/OctagonAI/octagon-vc-agents/blob/master/README.md
https://github.com/OctoEverywhere/mcp/blob/master/README.md
https://github.com/Omar-v2/mcp-ical/blob/master/README.md
https://github.com/Omedia/mcp-server-drupal/blob/master/README.md
https://github.com/ONLYOFFICE/docspace-mcp/blob/master/README.md
https://github.com/pab1it0/adx-mcp-server/blob/master/README.md
https://github.com/pab1it0/chess-mcp/blob/master/README.md
https://github.com/pab1it0/prometheus-mcp-server/blob/master/README.md
https://github.com/pab1it0/tripadvisor-mcp/blob/master/README.md
https://github.com/padmarajkore/hlf-fabric-agent/blob/master/README.md
https://github.com/pagos-ai/pagos-mcp/blob/master/README.md
https://github.com/paiml/paiml-mcp-agent-toolkit/blob/master/README.md
https://github.com/pansila/mcp_server_gdb/blob/master/README.md
https://github.com/paperinvest/mcp-server/blob/master/README.md
https://github.com/pathintegral-institute/blob/master/README.md
https://github.com/pathintegral-institute/mcpm.sh/blob/master/README.md
https://github.com/patronus-ai/patronus-mcp-server/blob/master/README.md
https://github.com/paulotaylor/voyp-mcp/blob/master/README.md
https://github.com/peakmojo/applescript-mcp/blob/master/README.md
https://github.com/peless/claude-thread-continuity/blob/master/README.md
https://github.com/pfldy2850/py-mcp-naver/blob/master/README.md
https://github.com/phimage/mcp-foundation-models/blob/master/README.md
https://github.com/phuc-nt/mcp-atlassian-server/blob/master/README.md
https://github.com/pinecone-io/assistant-mcp/blob/master/README.md
https://github.com/pinecone-io/pinecone-mcp/blob/master/README.md
https://github.com/pingcap/pytidb/blob/master/README.md
https://github.com/playcanvas/editor-mcp-server/blob/master/README.md
https://github.com/port-labs/port-mcp-server/blob/master/README.md
https://github.com/posthog/mcp/blob/master/README.md
https://github.com/powerdrillai/powerdrill-mcp/blob/master/README.md
https://github.com/ppl-ai/modelcontextprotocol/blob/master/README.md
https://github.com/prajwalnayak7/mcp-server-redis/blob/master/README.md
https://github.com/prashalruchiranga/arxiv-mcp-server/blob/master/README.md
https://github.com/privetin/chroma/blob/master/README.md
https://github.com/privetin/dataset-viewer/blob/master/README.md
https://github.com/privetin/stdict/blob/master/README.md
https://github.com/pubnub/pubnub-mcp-server/blob/master/README.md
https://github.com/pulumi/mcp-server/blob/master/README.md
https://github.com/punkpeye/blob/master/README.md
https://github.com/punkpeye/awesome-mcp-servers/blob/master/README.md
https://github.com/punkpeye/fastmcp/blob/master/README.md
https://github.com/puremd/puremd-mcp/blob/master/README.md
https://github.com/pureugong/mmk-mcp/blob/master/README.md
https://github.com/putdotio/putio-mcp-server/blob/master/README.md
https://github.com/pwilkin/mcp-searxng-public/blob/master/README.md
https://github.com/pydantic/logfire-mcp/blob/master/README.md
https://github.com/pydantic/pydantic-ai/tree/main/mcp-run-python/blob/master/README.md
https://github.com/pyroprompts/any-chat-completions-mcp/blob/master/README.md
https://github.com/pyroprompts/mcp-stdio-to-streamable-http-adapter/blob/master/README.md
https://github.com/PaddleHQ/paddle-mcp-server/blob/master/README.md
https://github.com/Pearl-com/pearl_mcp_server/blob/master/README.md
https://github.com/PipedreamHQ/pipedream/tree/master/modelcontextprotocol/blob/master/README.md
https://github.com/PortSwigger/mcp-server/blob/master/README.md
https://github.com/Prathamesh0901/zoom-mcp-server/tree/main/blob/master/README.md
https://github.com/ProfessionalWiki/MediaWiki-MCP-Server/blob/master/README.md
https://github.com/ProgramComputer/NASA-MCP-server/blob/master/README.md
https://github.com/PSPDFKit/nutrient-dws-mcp-server/blob/master/README.md
https://github.com/PV-Bhat/vibe-check-mcp-server/blob/master/README.md
https://github.com/qdrant/mcp-server-qdrant//blob/master/README.md
https://github.com/qiniu/qiniu-mcp-server/blob/master/README.md
https://github.com/quarkiverse/quarkus-mcp-server/blob/master/README.md
https://github.com/quarkiverse/quarkus-mcp-servers/blob/master/README.md
https://github.com/quarkiverse/quarkus-mcp-servers/tree/main/jdbc/blob/master/README.md
https://github.com/quarkiverse/quarkus-mcp-servers/tree/main/jfx/blob/master/README.md
https://github.com/quazaai/UnityMCPIntegration/blob/master/README.md
https://github.com/QAInsights/jmeter-mcp-server/blob/master/README.md
https://github.com/QuantGeekDev/blob/master/README.md
https://github.com/r-huijts/ns-mcp-server/blob/master/README.md
https://github.com/r-huijts/rijksmuseum-mcp/blob/master/README.md
https://github.com/r-huijts/strava-mcp/blob/master/README.md
https://github.com/r-huijts/xcode-mcp-server/blob/master/README.md
https://github.com/ragieai/ragie-mcp-server//blob/master/README.md
https://github.com/rajvirtual/MCP-Servers/tree/master/onenote/blob/master/README.md
https://github.com/rajvirtual/oura-mcp-server/blob/master/README.md
https://github.com/ramp-public/ramp-mcp/blob/master/README.md
https://github.com/raoulbia-ai/mcp-server-for-intercom/blob/master/README.md
https://github.com/ravenwits/mcp-server-arangodb/blob/master/README.md
https://github.com/ravinahp/blob/master/README.md
https://github.com/razorpay/razorpay-mcp-server/blob/master/README.md
https://github.com/razvanmacovei/k8s-multicluster-mcp/blob/master/README.md
https://github.com/reading-plus-ai/mcp-server-data-exploration/blob/master/README.md
https://github.com/reading-plus-ai/mcp-server-deep-research/blob/master/README.md
https://github.com/recraft-ai/mcp-recraft-server/blob/master/README.md
https://github.com/redis/mcp-redis-cloud//blob/master/README.md
https://github.com/redis/mcp-redis//blob/master/README.md
https://github.com/rember/rember-mcp/blob/master/README.md
https://github.com/renl/mcp-rag-local/blob/master/README.md
https://github.com/riemannzeta/patent_mcp_server/blob/master/README.md
https://github.com/rishabkoul/iTerm-MCP-Server/blob/master/README.md
https://github.com/rishijatia/fantasy-pl-mcp/blob/master/README.md
https://github.com/rishikavikondala/mcp-server-aws/blob/master/README.md
https://github.com/riza-io/riza-mcp/blob/master/README.md
https://github.com/rohans2/mcp-google-sheets/blob/master/README.md
https://github.com/root-signals/root-signals-mcp/blob/master/README.md
https://github.com/routineco/mcp-server/blob/master/README.md
https://github.com/ruixingshi/deepseek-thinker-mcp/blob/master/README.md
https://github.com/run-llama/mcp-server-llamacloud/blob/master/README.md
https://github.com/rust-mcp-stack/mcp-discovery/blob/master/README.md
https://github.com/rust-mcp-stack/rust-mcp-filesystem/blob/master/README.md
https://github.com/RafaelCartenet/mcp-databricks-server/blob/master/README.md
https://github.com/Rai220/think-mcp/blob/master/README.md
https://github.com/RamXX/mcp-tavily/blob/master/README.md
https://github.com/RapidataAI/human-use/blob/master/README.md
https://github.com/ReexpressAI/reexpress_mcp_server/blob/master/README.md
https://github.com/Roblox/studio-rust-mcp-server/blob/master/README.md
https://github.com/safedep/pinner-mcp/blob/master/README.md
https://github.com/safedep/vet/blob/main/docs/mcp.md/blob/master/README.md
https://github.com/sakce/mcp-server-monday/blob/master/README.md
https://github.com/salesforce-mcp/salesforce-mcp/blob/master/README.md
https://github.com/samuelgursky/davinci-resolve-mcp/blob/master/README.md
https://github.com/scorzeth/anki-mcp-server/blob/master/README.md
https://github.com/scottlepp/loki-mcp/blob/master/README.md
https://github.com/scottlepp/tempo-mcp-server/blob/master/README.md
https://github.com/screenshotone/mcp//blob/master/README.md
https://github.com/semgrep/mcp/blob/master/README.md
https://github.com/sendaifun/solana-agent-kit/tree/main/examples/agent-kit-mcp-server/blob/master/README.md
https://github.com/sergehuber/inoyu-mcp-unomi-server/blob/master/README.md
https://github.com/severity1/terraform-cloud-mcp/blob/master/README.md
https://github.com/shanejonas/openrpc-mpc-server/blob/master/README.md
https://github.com/shannonlal/mcp-postman/blob/master/README.md
https://github.com/shinzo-labs/coinmarketcap-mcp/blob/master/README.md
https://github.com/singlestore-labs/mcp-server-singlestore/blob/master/README.md
https://github.com/sirmews/mcp-pinecone/blob/master/README.md
https://github.com/skydeckai/mcp-server-rememberizer/blob/master/README.md
https://github.com/smn2gnt/MCP-Salesforce/blob/master/README.md
https://github.com/snagasuri/deebo-prototype/blob/master/README.md
https://github.com/snaggle-ai/openapi-mcp-server/blob/master/README.md
https://github.com/snyk/snyk-ls/blob/main/mcp_extension/blob/master/README.md/blob/master/README.md
https://github.com/softeria/ms-365-mcp-server/blob/master/README.md
https://github.com/sonnylazuardi/cursor-talk-to-figma-mcp/blob/master/README.md
https://github.com/sooperset/mcp-atlassian/blob/master/README.md
https://github.com/sophtron/Sophtron-Integration/tree/main/modelcontextprotocol/blob/master/README.md
https://github.com/sparfenyuk/mcp-proxy/blob/master/README.md
https://github.com/spgoodman/createveai-nexus-server/blob/master/README.md
https://github.com/sssjiang/pubchem_mcp_server/blob/master/README.md
https://github.com/stass/lldb-mcp/blob/master/README.md
https://github.com/steadybit/mcp/blob/master/README.md
https://github.com/stefanoamorelli/fred-mcp-server/blob/master/README.md
https://github.com/stefanoamorelli/hyprmcp/blob/master/README.md
https://github.com/stefanoamorelli/nasdaq-data-link-mcp/blob/master/README.md
https://github.com/stefanoamorelli/sec-edgar-mcp/blob/master/README.md
https://github.com/stippi/code-assistant/blob/master/README.md
https://github.com/stripe/agent-toolkit/blob/master/README.md
https://github.com/strowk/blob/master/README.md
https://github.com/strowk/foxy-contexts/blob/master/README.md
https://github.com/strowk/mcp-k8s-go/blob/master/README.md
https://github.com/suekou/mcp-notion-server/blob/master/README.md
https://github.com/suhail-ak-s/mcp-typesense-server/blob/master/README.md
https://github.com/sunsetcoder/flightradar24-mcp-server/blob/master/README.md
https://github.com/surendranb/google-analytics-mcp/blob/master/README.md
https://github.com/sv/mcp-paradex-py/blob/master/README.md
https://github.com/svkaizoku/mcp-bvg/blob/master/README.md
https://github.com/syronlabs/stellar-mcp//blob/master/README.md
https://github.com/syucream/holaspirit-mcp-server/blob/master/README.md
https://github.com/syucream/lightdash-mcp-server/blob/master/README.md
https://github.com/szeider/consult7/blob/master/README.md
https://github.com/szeider/mcp-dblp/blob/master/README.md
https://github.com/szeider/mcp-solver/blob/master/README.md
https://github.com/SaseQ/discord-mcp/blob/master/README.md
https://github.com/SerhatUzbas/mcp-server-generator/blob/master/README.md
https://github.com/ShenghaiWang/xcodebuild/blob/master/README.md
https://github.com/Shy2593666979/mcp-server-email/blob/master/README.md
https://github.com/Simon-Kansara/ableton-live-mcp-server/blob/master/README.md
https://github.com/SimonB97/win-cli-mcp-server/blob/master/README.md
https://github.com/Skyvern-AI/skyvern/tree/main/integrations/mcp/blob/master/README.md
https://github.com/SlideSpeak/slidespeak-mcp/blob/master/README.md
https://github.com/Spathodea-Network/opencti-mcp/blob/master/README.md
https://github.com/StacklokLabs/blob/master/README.md
https://github.com/StacklokLabs/toolhive/blob/master/README.md
https://github.com/StarRocks/mcp-server-starrocks/blob/master/README.md
https://github.com/StevenStavrakis/obsidian-mcp/blob/master/README.md
https://github.com/StitchAI/stitch-ai-mcp//blob/master/README.md
https://github.com/Super-I-Tech/mcp_plexus/blob/master/README.md
https://github.com/tacticlaunch/mcp-linear/blob/master/README.md
https://github.com/tadasant/blob/master/README.md
https://github.com/tadata-org/fastapi_mcp/blob/master/README.md
https://github.com/takashiishida/arxiv-latex-mcp/blob/master/README.md
https://github.com/tavily-ai/tavily-mcp/blob/master/README.md
https://github.com/taylorwilsdon/google_workspace_mcp/blob/master/README.md
https://github.com/teddyzxcv/ntfy-mcp/blob/master/README.md
https://github.com/tesla0225/mcp-create/blob/master/README.md
https://github.com/tevonsb/homeassistant-mcp/blob/master/README.md
https://github.com/thanhtung0201/mcp-remote-system-health/blob/master/README.md
https://github.com/thirdweb-dev/ai/tree/main/python/thirdweb-mcp/blob/master/README.md
https://github.com/thoughtspot/mcp-server/blob/master/README.md
https://github.com/tinybirdco/mcp-tinybird/blob/master/README.md
https://github.com/tinyfish-io/agentql-mcp/blob/master/README.md
https://github.com/tipdotmd#-mcp-server-for-ai-assistants/blob/master/README.md
https://github.com/tomelliot/todos-mcp/blob/master/README.md
https://github.com/tonyzorin/youtrack-mcp/blob/master/README.md
https://github.com/topoteretes/cognee/tree/main/cognee-mcp/blob/master/README.md
https://github.com/translated/lara-mcp/blob/master/README.md
https://github.com/trypeggy/facebook-ads-library-mcp/blob/master/README.md
https://github.com/tsmztech/mcp-server-salesforce/blob/master/README.md
https://github.com/ttommyth/interactive-mcp/blob/master/README.md
https://github.com/twilio-labs/mcp/blob/master/README.md
https://github.com/tymonTe/gralio-mcp/blob/master/README.md
https://github.com/TaazKareem/clickup-mcp-server/blob/master/README.md
https://github.com/Taidgh-Robinson/nba-mcp-server/blob/master/README.md
https://github.com/TakoData/tako-mcp/blob/master/README.md
https://github.com/TBXark/mcp-proxy/blob/master/README.md
https://github.com/TencentEdgeOne/edgeone-pages-mcp/blob/master/README.md
https://github.com/TermiX-official/bsc-mcp/blob/master/README.md
https://github.com/TimLukaHorstmann/mcp-weather/blob/master/README.md
https://github.com/TocharianOU/mcp-server-kibana.git/blob/master/README.md
https://github.com/Trade-Agent/trade-agent-mcp/blob/master/README.md
https://github.com/Tritlo/lsp-mcp/blob/master/README.md
https://github.com/TSavo/creatify-mcp/blob/master/README.md
https://github.com/TykTechnologies/tyk-dashboard-mcp/blob/master/README.md
https://github.com/ubie-oss/mcp-vertexai-search/blob/master/README.md
https://github.com/unibaseio/membase-mcp/blob/master/README.md
https://github.com/unifai-network/unifai-mcp-server/blob/master/README.md
https://github.com/universal-mcp/calendly/blob/master/README.md
https://github.com/universal-mcp/coda/blob/master/README.md
https://github.com/universal-mcp/digitalocean/blob/master/README.md
https://github.com/universal-mcp/falai/blob/master/README.md
https://github.com/universal-mcp/google-docs/blob/master/README.md
https://github.com/universal-mcp/google-searchconsole/blob/master/README.md
https://github.com/universal-mcp/hashnode/blob/master/README.md
https://github.com/universal-mcp/reddit/blob/master/README.md
https://github.com/universal-mcp/serpapi/blob/master/README.md
https://github.com/upstash/mcp-server/blob/master/README.md
https://github.com/urbanogardun/things3-mcp/blob/master/README.md
https://github.com/useparagon/paragon-mcp/blob/master/README.md
https://github.com/useshortcut/mcp-server-shortcut/blob/master/README.md
https://github.com/utensils/mcp-nixos/blob/master/README.md
https://github.com/Unstructured-IO/UNS-MCP/blob/master/README.md
https://github.com/v-3/discordmcp/blob/master/README.md
https://github.com/v-3/google-calendar/blob/master/README.md
https://github.com/v-3/notion-server/blob/master/README.md
https://github.com/vantage-sh/vantage-mcp-server/blob/master/README.md
https://github.com/variflight/variflight-mcp/blob/master/README.md
https://github.com/varunneal/spotify-mcp/blob/master/README.md
https://github.com/vectara/vectara-mcp/blob/master/README.md
https://github.com/vectorize-io/vectorize-mcp-server//blob/master/README.md
https://github.com/verbwire/verbwire-mcp-server/blob/master/README.md
https://github.com/vercel-labs/mcp-for-next.js/blob/master/README.md
https://github.com/vercel/mcp-adapter/blob/master/README.md
https://github.com/vespo92/OPNSenseMCP/blob/master/README.md
https://github.com/vespo92/TrueNasCoreMCP/blob/master/README.md
https://github.com/vgnshiyer/apple-books-mcp/blob/master/README.md
https://github.com/video-db/agent-toolkit/tree/main/modelcontextprotocol/blob/master/README.md
https://github.com/vidhupv/x-mcp/blob/master/README.md
https://github.com/vincent-pli/mcp-cli-host/blob/master/README.md
https://github.com/vishalmysore/choturobo/blob/master/README.md
https://github.com/vivekVells/mcp-pandoc/blob/master/README.md
https://github.com/voska/hass-mcp/blob/master/README.md
https://github.com/vrknetha/mcp-server-firecrawl/blob/master/README.md
https://github.com/VectorInstitute/mcp-goodnews/blob/master/README.md
https://github.com/VeriTeknik/pluggedin-mcp/blob/master/README.md
https://github.com/Verodat/verodat-mcp-server/blob/master/README.md
https://github.com/VeyraX/veyrax-mcp/blob/master/README.md
https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/blob/master/README.md
https://github.com/VivekKumarNeu/MCP-Lucene-Server/blob/master/README.md
https://github.com/Vortiago/mcp-azure-devops/blob/master/README.md
https://github.com/Vortiago/mcp-outline/blob/master/README.md
https://github.com/wanaku-ai/wanaku//blob/master/README.md
https://github.com/wangshunnn/bilibili-mcp-server/blob/master/README.md
https://github.com/waystation-ai/mcp/blob/master/README.md
https://github.com/webflow/mcp-server/blob/master/README.md
https://github.com/webscraping-ai/webscraping-ai-mcp-server/blob/master/README.md
https://github.com/whataboutyou-ai/eunomia-MCP-server/blob/master/README.md
https://github.com/wildfly-extras/wildfly-mcp/blob/master/README.md
https://github.com/wilson-urdaneta/chesspal-mcp-engine/blob/master/README.md
https://github.com/wilsonchenghy/ShaderToy-MCP/blob/master/README.md
https://github.com/wonderwhy-er/DesktopCommanderMCP/blob/master/README.md
https://github.com/wong2/blob/master/README.md
https://github.com/wong2/awesome-mcp-servers/blob/master/README.md
https://github.com/wong2/mcp-cli/blob/master/README.md
https://github.com/wuye-ai/mcp-server-wuye-ai/blob/master/README.md
https://github.com/WaveSpeedAI/mcp-server/blob/master/README.md
https://github.com/xing5/mcp-google-sheets/blob/master/README.md
https://github.com/xxxbrian/mcp-rquest/blob/master/README.md
https://github.com/XeroAPI/xero-mcp-server/blob/master/README.md
https://github.com/XGenerationLab/xiyan_mcp_server/blob/master/README.md
https://github.com/yangkyeongmo/mcp-server-apache-airflow/blob/master/README.md
https://github.com/yanmxa/multicluster-mcp-server/blob/master/README.md
https://github.com/yashshingvi/databricks-genie-MCP/blob/master/README.md
https://github.com/ydb-platform/ydb-mcp/blob/master/README.md
https://github.com/yeonupark/mcp-soccer-data/blob/master/README.md
https://github.com/yepcode/mcp-server-js/blob/master/README.md
https://github.com/yoelbassin/gnuradioMCP/blob/master/README.md
https://github.com/yuanoOo/oceanbase_mcp_server/blob/master/README.md
https://github.com/yugabyte/yugabytedb-mcp-server/blob/master/README.md
https://github.com/yuutotsuki/tsuki_mcp_filesystem_server/blob/master/README.md
https://github.com/YanxingLiu/dify-mcp-server/blob/master/README.md
https://github.com/YuChenSSR/mindmap-mcp-server/blob/master/README.md
https://github.com/YuChenSSR/multi-ai-advisor-mcp/blob/master/README.md
https://github.com/zaiwork/mcp/blob/master/README.md
https://github.com/zcaceres/easy-mcp//blob/master/README.md
https://github.com/zcaceres/fetch-mcp/blob/master/README.md
https://github.com/zcaceres/gtasks-mcp/blob/master/README.md
https://github.com/zcaceres/mcp-markdownify-server/blob/master/README.md
https://github.com/zenml-io/mcp-zenml/blob/master/README.md
https://github.com/zilliztech/mcp-server-milvus/blob/master/README.md
https://github.com/zueai/blob/master/README.md
https://github.com/zueai/mcp-manager/blob/master/README.md
https://github.com/zzaebok/mcp-wikidata/blob/master/README.md
https://github.com/ZilongXue/claude-post/blob/master/README.md
https://github.com/ZubeidHendricks/youtube-mcp-server/blob/master/README.md
https://gitlab.com/tldv/tldv-mcp-server/blob/main/README.md
https://raw.githubusercontent.com/modelcontextprotocol/servers/main/src/everything/README.md
https://raw.githubusercontent.com/modelcontextprotocol/servers/main/src/fetch/README.md
https://raw.githubusercontent.com/modelcontextprotocol/servers/main/src/filesystem/README.md
https://raw.githubusercontent.com/modelcontextprotocol/servers/main/src/git/README.md
https://raw.githubusercontent.com/modelcontextprotocol/servers/main/src/memory/README.md
https://raw.githubusercontent.com/modelcontextprotocol/servers/main/src/sequentialthinking/README.md
https://raw.githubusercontent.com/modelcontextprotocol/servers/main/src/time/README.md