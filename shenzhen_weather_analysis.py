#!/usr/bin/env python3
"""
分析深圳过去7天的天气情况
使用OpenWeatherMap API获取历史天气数据
"""

import requests
import json
from datetime import datetime, timedelta
import time

# 深圳坐标
SHENZHEN_LAT = 22.542883
SHENZHEN_LON = 114.062996

# OpenWeatherMap API配置
# 注意：需要有效的API密钥才能使用
API_KEY = "YOUR_API_KEY_HERE"  # 需要替换为实际的API密钥
BASE_URL = "https://history.openweathermap.org/data/2.5/history/city"

def get_unix_timestamp(days_ago):
    """获取指定天数前的Unix时间戳"""
    target_date = datetime.now() - timedelta(days=days_ago)
    return int(target_date.timestamp())

def fetch_weather_data(start_timestamp, end_timestamp):
    """获取指定时间范围的天气数据"""
    url = f"{BASE_URL}?lat={SHENZHEN_LAT}&lon={SHENZHEN_LON}&type=hour&start={start_timestamp}&end={end_timestamp}&appid={API_KEY}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"API请求失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"请求错误: {e}")
        return None

def analyze_weather_data(weather_data):
    """分析天气数据"""
    if not weather_data or 'list' not in weather_data:
        return None
    
    weather_list = weather_data['list']
    
    # 按天分组数据
    daily_data = {}
    
    for entry in weather_list:
        # 转换时间戳为日期
        dt = datetime.fromtimestamp(entry['dt'])
        date_key = dt.strftime('%Y-%m-%d')
        
        if date_key not in daily_data:
            daily_data[date_key] = []
        
        daily_data[date_key].append(entry)
    
    # 分析每日数据
    daily_analysis = {}
    
    for date, entries in daily_data.items():
        temps = [entry['main']['temp'] - 273.15 for entry in entries]  # 转换为摄氏度
        humidity = [entry['main']['humidity'] for entry in entries]
        pressure = [entry['main']['pressure'] for entry in entries]
        
        # 获取天气描述
        weather_descriptions = [entry['weather'][0]['description'] for entry in entries]
        main_weather = max(set(weather_descriptions), key=weather_descriptions.count)
        
        daily_analysis[date] = {
            'avg_temp': round(sum(temps) / len(temps), 1),
            'max_temp': round(max(temps), 1),
            'min_temp': round(min(temps), 1),
            'avg_humidity': round(sum(humidity) / len(humidity), 1),
            'avg_pressure': round(sum(pressure) / len(pressure), 1),
            'main_weather': main_weather,
            'data_points': len(entries)
        }
    
    return daily_analysis

def generate_weather_report(daily_analysis):
    """生成天气报告"""
    if not daily_analysis:
        return "无法生成天气报告：数据不足"
    
    report = "# 深圳过去7天天气分析报告\n\n"
    report += f"分析时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    report += f"地点：深圳市 (纬度: {SHENZHEN_LAT}, 经度: {SHENZHEN_LON})\n\n"
    
    # 按日期排序
    sorted_dates = sorted(daily_analysis.keys())
    
    report += "## 每日天气详情\n\n"
    
    for date in sorted_dates:
        data = daily_analysis[date]
        report += f"### {date}\n"
        report += f"- **平均温度**: {data['avg_temp']}°C\n"
        report += f"- **最高温度**: {data['max_temp']}°C\n"
        report += f"- **最低温度**: {data['min_temp']}°C\n"
        report += f"- **平均湿度**: {data['avg_humidity']}%\n"
        report += f"- **平均气压**: {data['avg_pressure']} hPa\n"
        report += f"- **主要天气**: {data['main_weather']}\n"
        report += f"- **数据点数**: {data['data_points']}\n\n"
    
    # 总体分析
    all_avg_temps = [data['avg_temp'] for data in daily_analysis.values()]
    all_max_temps = [data['max_temp'] for data in daily_analysis.values()]
    all_min_temps = [data['min_temp'] for data in daily_analysis.values()]
    all_humidity = [data['avg_humidity'] for data in daily_analysis.values()]
    
    report += "## 7天总体分析\n\n"
    report += f"- **平均温度范围**: {min(all_avg_temps)}°C - {max(all_avg_temps)}°C\n"
    report += f"- **最高温度**: {max(all_max_temps)}°C\n"
    report += f"- **最低温度**: {min(all_min_temps)}°C\n"
    report += f"- **平均湿度**: {round(sum(all_humidity) / len(all_humidity), 1)}%\n"
    report += f"- **分析天数**: {len(daily_analysis)}天\n\n"
    
    # 天气趋势
    report += "## 天气趋势\n\n"
    if len(all_avg_temps) >= 2:
        temp_trend = "上升" if all_avg_temps[-1] > all_avg_temps[0] else "下降"
        report += f"- **温度趋势**: 整体呈{temp_trend}趋势\n"
    
    return report

def main():
    """主函数"""
    print("开始分析深圳过去7天的天气情况...")
    
    # 计算时间范围（过去7天）
    end_timestamp = get_unix_timestamp(1)  # 昨天
    start_timestamp = get_unix_timestamp(8)  # 8天前
    
    print(f"时间范围: {datetime.fromtimestamp(start_timestamp)} 到 {datetime.fromtimestamp(end_timestamp)}")
    
    # 由于API限制，需要分批获取数据（每次最多一周）
    weather_data = fetch_weather_data(start_timestamp, end_timestamp)
    
    if weather_data:
        print("成功获取天气数据，开始分析...")
        daily_analysis = analyze_weather_data(weather_data)
        
        if daily_analysis:
            report = generate_weather_report(daily_analysis)
            
            # 保存报告
            with open('shenzhen_weather_report.md', 'w', encoding='utf-8') as f:
                f.write(report)
            
            print("分析完成！报告已保存到 shenzhen_weather_report.md")
            print("\n" + "="*50)
            print(report)
        else:
            print("数据分析失败")
    else:
        print("无法获取天气数据，请检查API密钥和网络连接")

if __name__ == "__main__":
    main()
