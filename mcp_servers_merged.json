{"repository_info": {"name": "modelcontextprotocol/servers", "description": "Model Context Protocol Servers - Reference implementations and community-built servers", "url": "https://github.com/modelcontextprotocol/servers"}, "root_package": {"name": "@modelcontextprotocol/servers", "private": true, "version": "0.6.2", "description": "Model Context Protocol servers", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "workspaces": ["src/*"], "files": [], "scripts": {"build": "npm run build --workspaces", "watch": "npm run watch --workspaces", "publish-all": "npm publish --workspaces --access public", "link-all": "npm link --workspaces"}, "dependencies": {"@modelcontextprotocol/server-everything": "*", "@modelcontextprotocol/server-memory": "*", "@modelcontextprotocol/server-filesystem": "*", "@modelcontextprotocol/server-sequential-thinking": "*"}}, "root_tsconfig": {"compilerOptions": {"target": "ES2022", "module": "Node16", "moduleResolution": "Node16", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules"]}, "servers": {"filesystem": {"package": {"name": "@modelcontextprotocol/server-filesystem", "version": "0.6.2", "description": "MCP server for filesystem access", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-filesystem": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch", "test": "jest --config=jest.config.cjs --coverage"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.3", "diff": "^5.1.0", "glob": "^10.3.10", "minimatch": "^10.0.1", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/diff": "^5.0.9", "@types/jest": "^29.5.14", "@types/minimatch": "^5.1.2", "@types/node": "^22", "jest": "^29.7.0", "shx": "^0.3.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}, "tsconfig": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": ".", "moduleResolution": "NodeNext", "module": "NodeNext"}, "include": ["./**/*.ts"]}, "jest_config": {"preset": "ts-jest", "testEnvironment": "node", "extensionsToTreatAsEsm": [".ts"], "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "transform": {"^.+\\.tsx?$": ["ts-jest", {"useESM": true}]}, "testMatch": ["**/__tests__/**/*.test.ts"], "collectCoverageFrom": ["**/*.ts", "!**/__tests__/**", "!**/dist/**"]}}, "memory": {"package": {"name": "@modelcontextprotocol/server-memory", "version": "0.6.3", "description": "MCP server for enabling memory for <PERSON> through a knowledge graph", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-memory": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "1.0.1"}, "devDependencies": {"@types/node": "^22", "shx": "^0.3.4", "typescript": "^5.6.2"}}, "tsconfig": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "."}, "include": ["./**/*.ts"]}}, "everything": {"package": {"name": "@modelcontextprotocol/server-everything", "version": "0.6.2", "description": "MCP server that exercises all the features of the MCP protocol", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-everything": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx cp instructions.md dist/ && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch", "start": "node dist/index.js", "start:sse": "node dist/sse.js", "start:streamableHttp": "node dist/streamableHttp.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "express": "^4.21.1", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@types/express": "^5.0.0", "shx": "^0.3.4", "typescript": "^5.6.2"}}, "tsconfig": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "."}, "include": ["./**/*.ts"]}}, "sequentialthinking": {"package": {"name": "@modelcontextprotocol/server-sequential-thinking", "version": "0.6.2", "description": "MCP server for sequential thinking and problem solving", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-sequential-thinking": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "0.5.0", "chalk": "^5.3.0", "yargs": "^17.7.2"}, "devDependencies": {"@types/node": "^22", "@types/yargs": "^17.0.32", "shx": "^0.3.4", "typescript": "^5.3.3"}}, "tsconfig": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": ".", "moduleResolution": "NodeNext", "module": "NodeNext"}, "include": ["./**/*.ts"]}}}, "extraction_metadata": {"extraction_date": "2025-01-01", "source_repository": "https://github.com/modelcontextprotocol/servers", "extracted_files_count": 11, "file_types": ["package.json", "tsconfig.json", "jest.config.cjs"], "servers_found": ["filesystem", "memory", "everything", "sequentialthinking"], "notes": "This merged JSON contains all JSON configuration files found in the MCP servers repository, including package.json files for each server, TypeScript configurations, and Jest test configurations where available."}}